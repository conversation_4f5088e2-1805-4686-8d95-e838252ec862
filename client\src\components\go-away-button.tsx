import React from 'react';
import { ArrowRight } from 'lucide-react';

interface GoAwayButtonProps {
  onClick: () => void;
  className?: string;
}

const GoAwayButton: React.FC<GoAwayButtonProps> = ({ onClick, className = '' }) => {
  return (
    <button
      onClick={onClick}
      className={`fixed bottom-4 left-4 z-50 bg-red-500 dark:bg-red-600 border border-red-600 dark:border-red-700 rounded-lg px-4 py-3 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center space-x-2 text-base font-semibold text-white hover:bg-red-600 dark:hover:bg-red-700 ${className}`}
      title="Make the robot go away"
    >
      <ArrowRight className="h-5 w-5" />
      <span>go away</span>
    </button>
  );
};

export default GoAwayButton;
