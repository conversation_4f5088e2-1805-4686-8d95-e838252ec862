// Wallet Database Configuration
// This file contains the configuration for connecting to the separate wallet database

// Separate wallet database - completely independent from main app databases
export const WALLET_DATABASE_CONFIG = {
  // Supabase configuration for wallet database
  SUPABASE_URL: 'https://mjyaqqsxhkqyzqufpxzl.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs',
  POOLER_URI: 'postgresql://postgres.mjyaqqsxhkqyzqufpxzl:[PASSWORD]@aws-0-eu-west-2.pooler.supabase.com:6543/postgres',

  // Table names in the wallet database
  TABLES: {
    WALLETS: 'wallets',
    WALLET_SESSIONS: 'wallet_sessions',
    TRANSACTIONS: 'transactions',
    WALLET_CONNECTIONS: 'wallet_connections'
  },

  // Demo wallet credentials (NO BALANCE - balances stored in user accounts)
  DEMO_WALLET: {
    WALLET_ID: 'demo-wallet',
    PASSWORD: 'demo123'
    // REMOVED: BALANCE - balances are now stored in user accounts in main databases
  },

  // Session configuration
  SESSION: {
    EXPIRES_DAYS: 7,
    TOKEN_PREFIX: 'wallet_'
  }
};

// Utility functions for wallet operations
export const WalletUtils = {
  // Generate a session token
  generateSessionToken() {
    return WALLET_DATABASE_CONFIG.SESSION.TOKEN_PREFIX +
           Math.random().toString(36).substr(2, 9) +
           Date.now().toString(36);
  },

  // Calculate session expiry date
  getSessionExpiry() {
    const expiry = new Date();
    expiry.setDate(expiry.getDate() + WALLET_DATABASE_CONFIG.SESSION.EXPIRES_DAYS);
    return expiry.toISOString();
  },

  // REMOVED: formatBalance - balances are now managed in user accounts, not wallets

  // Validate wallet ID
  validateWalletId(walletId) {
    if (!walletId || walletId.length < 3) {
      return { valid: false, error: 'Wallet ID must be at least 3 characters' };
    }
    if (walletId.length > 50) {
      return { valid: false, error: 'Wallet ID must be less than 50 characters' };
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(walletId)) {
      return { valid: false, error: 'Wallet ID can only contain letters, numbers, hyphens, and underscores' };
    }
    return { valid: true };
  },

  // Validate password
  validatePassword(password) {
    if (!password || password.length < 6) {
      return { valid: false, error: 'Password must be at least 6 characters' };
    }
    if (password.length > 128) {
      return { valid: false, error: 'Password must be less than 128 characters' };
    }
    return { valid: true };
  },

  // Hash password using simple method (in production, use proper scrypt)
  async hashPassword(password) {
    // For demo purposes, we'll use a simple hash
    // In production, this should use scrypt like the main database
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'wallet_salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  },

  // Verify password
  async verifyPassword(password, storedHash) {
    const computedHash = await this.hashPassword(password);
    return computedHash === storedHash;
  }
};

// Wallet API functions
export const WalletAPI = {
  // Initialize Supabase client for wallet database
  async initClient() {
    if (typeof window.supabase === 'undefined') {
      throw new Error('Supabase not loaded');
    }

    return window.supabase.createClient(
      WALLET_DATABASE_CONFIG.SUPABASE_URL,
      WALLET_DATABASE_CONFIG.SUPABASE_KEY
    );
  },

  // Login to wallet
  async loginWallet(walletId, password) {
    try {
      // Check demo wallet first
      if (walletId === WALLET_DATABASE_CONFIG.DEMO_WALLET.WALLET_ID &&
          password === WALLET_DATABASE_CONFIG.DEMO_WALLET.PASSWORD) {

        const demoWallet = {
          id: 'demo-wallet-uuid',
          wallet_id: WALLET_DATABASE_CONFIG.DEMO_WALLET.WALLET_ID,
          // REMOVED: balance - balances are now stored in user accounts in main databases
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        };

        const sessionToken = WalletUtils.generateSessionToken();
        const expiresAt = WalletUtils.getSessionExpiry();

        return {
          success: true,
          wallet: demoWallet,
          session: {
            token: sessionToken,
            expires_at: expiresAt
          }
        };
      }

      // TODO: Implement actual database lookup
      // const client = await this.initClient();
      // const { data: wallets, error } = await client
      //   .from(WALLET_DATABASE_CONFIG.TABLES.WALLETS)
      //   .select('*')
      //   .eq('wallet_id', walletId)
      //   .eq('is_active', true)
      //   .single();

      return { success: false, error: 'Invalid wallet ID or password' };

    } catch (error) {
      console.error('Wallet login error:', error);
      return { success: false, error: 'Login failed. Please try again.' };
    }
  },

  // Create new wallet
  async createWallet(walletId, password) {
    try {
      // Validate inputs
      const walletIdValidation = WalletUtils.validateWalletId(walletId);
      if (!walletIdValidation.valid) {
        return { success: false, error: walletIdValidation.error };
      }

      const passwordValidation = WalletUtils.validatePassword(password);
      if (!passwordValidation.valid) {
        return { success: false, error: passwordValidation.error };
      }

      // For demo purposes, create a local wallet (NO BALANCE)
      const newWallet = {
        id: 'wallet-' + Date.now(),
        wallet_id: walletId,
        // REMOVED: balance - balances are now stored in user accounts in main databases
        created_at: new Date().toISOString(),
        last_accessed: new Date().toISOString()
      };

      const sessionToken = WalletUtils.generateSessionToken();
      const expiresAt = WalletUtils.getSessionExpiry();

      // TODO: Implement actual database creation
      // const client = await this.initClient();
      // const passwordHash = await WalletUtils.hashPassword(password);
      // const { data, error } = await client
      //   .from(WALLET_DATABASE_CONFIG.TABLES.WALLETS)
      //   .insert({
      //     wallet_id: walletId,
      //     password_hash: passwordHash
      //     // REMOVED: balance - balances are now stored in user accounts
      //   })
      //   .select()
      //   .single();

      return {
        success: true,
        wallet: newWallet,
        session: {
          token: sessionToken,
          expires_at: expiresAt
        }
      };

    } catch (error) {
      console.error('Wallet creation error:', error);
      return { success: false, error: 'Failed to create wallet. Please try again.' };
    }
  },

  // REMOVED: getWalletBalance - balances are now stored in user accounts in main databases
  // Wallets no longer store or manage balances directly

  // Create transaction
  async createTransaction(walletId, type, amount, description) {
    try {
      // TODO: Implement actual transaction creation
      return { success: true, transaction_id: 'tx-' + Date.now() };
    } catch (error) {
      console.error('Error creating transaction:', error);
      return { success: false, error: 'Failed to create transaction' };
    }
  }
};
