# User-Owned DasWos Coins Architecture

## Overview

This document describes the new architecture where **users own their DasWos coins balance** and **wallets serve as interfaces** for accessing and using that balance.

## Key Principles

1. **Users own the balance** - DasWos coins are stored in user accounts in the main database
2. **Wallets are interfaces** - Wallets provide access to user balance but don't store balance themselves
3. **Authentication required** - Users must be logged in to access their balance
4. **Wallet connection recommended** - Wallet connection provides enhanced security and audit trail
5. **Balance persistence** - User balance persists across different wallet connections

## Architecture Components

### 1. User Account (Main Database)
- **Location**: `users` table in main database (daswos-18)
- **Balance Storage**: `daswos_coins_balance` column
- **Wallet Link**: `wallet_id` column (links to user's preferred wallet)
- **Ownership**: User owns the balance, not the wallet

### 2. Wallet System (Separate Database)
- **Purpose**: Authentication and interface only
- **No Balance Storage**: Wallets no longer store DasWos coins balance
- **Connection Tracking**: Links wallets to user accounts for audit
- **Security**: Provides secure access to user balance

### 3. Transaction System
- **Location**: `daswos_coins_transactions` table in main database
- **Tracking**: Records all balance changes with wallet audit trail
- **Wallet ID**: Tracks which wallet was used for each transaction
- **User-Centric**: All transactions tied to user accounts

## User Experience Flow

### 1. User Login Only
```
User logs in → Can see balance (0 if no wallet linked)
                ↓
              Balance shows but with recommendation to link wallet
```

### 2. User Login + Wallet Connection
```
User logs in → Connects wallet → Balance shows with full access
                                    ↓
                                 Can purchase/spend coins
```

### 3. Wallet Verification
```
User has linked wallet → Connects different wallet → Balance shows with warning
                                                        ↓
                                                   Recommends connecting correct wallet
```

## Database Schema Changes

### Users Table (Main Database)
```sql
ALTER TABLE users ADD COLUMN daswos_coins_balance INTEGER DEFAULT 0 NOT NULL;
ALTER TABLE users ADD COLUMN wallet_id TEXT; -- Links to user's wallet
```

### Wallets Table (Wallet Database)
```sql
-- REMOVED: balance column
-- Wallets no longer store balance information
```

### Transactions Table (Main Database)
```sql
ALTER TABLE daswos_coins_transactions ADD COLUMN wallet_id TEXT;
-- Tracks which wallet was used for audit purposes
```

## API Behavior

### Balance Endpoint
- **Authentication**: User must be logged in
- **Wallet Verification**: Optional wallet_id parameter for verification
- **Response**: Includes balance, wallet access status, and recommendations

### Purchase Endpoint
- **Requirements**: User authentication required
- **Wallet Tracking**: Records which wallet was used
- **Balance Update**: Adds to user's account balance
- **Wallet Linking**: Can link wallet to user account during purchase

### Spend Endpoint
- **Requirements**: User authentication required
- **Balance Check**: Verifies user has sufficient balance in account
- **Wallet Tracking**: Records which wallet was used for audit
- **Balance Update**: Deducts from user's account balance

## Security Features

### 1. User Authentication
- Users must be logged in to access any balance information
- No balance access without proper authentication

### 2. Wallet Verification
- System tracks which wallet is linked to each user
- Warns if user connects different wallet than expected
- Provides recommendations for proper wallet connection

### 3. Audit Trail
- All transactions record which wallet was used
- Complete history of balance changes with wallet attribution
- User-centric transaction history

## Migration Strategy

### 1. Database Migration
- Add `wallet_id` column to users table
- Add `wallet_id` column to transactions table
- Remove balance storage from wallet database

### 2. Code Updates
- Update API endpoints to check user authentication
- Modify wallet hooks to remove balance storage
- Update frontend to handle new wallet verification flow

### 3. Data Migration
- Existing wallet balances can be migrated to user accounts
- Link existing wallets to user accounts where possible
- Preserve transaction history with wallet attribution

## Benefits

### 1. Clear Ownership
- Users clearly own their DasWos coins balance
- Balance persists regardless of wallet changes
- No confusion about where balance is stored

### 2. Enhanced Security
- User authentication required for all balance access
- Wallet verification provides additional security layer
- Complete audit trail of all transactions

### 3. Flexibility
- Users can change wallets without losing balance
- Multiple wallets can be linked to same user account
- Balance access doesn't depend on specific wallet

### 4. Scalability
- Centralized balance management in main database
- Wallet system can focus on authentication only
- Easier to implement new wallet types or providers

## Implementation Status

✅ **Completed**:
- Database schema updates
- API endpoint modifications
- Frontend hook updates
- Migration scripts created
- Documentation completed

🔄 **Next Steps**:
- Run database migration
- Test user login + wallet connection flow
- Verify balance display behavior
- Test purchase and spend operations
- Validate wallet verification warnings

## Testing Scenarios

### 1. New User Flow
1. User creates account → Balance: 0
2. User connects wallet → Balance: 0, wallet linked
3. User purchases coins → Balance: updated, transaction recorded
4. User spends coins → Balance: updated, transaction recorded

### 2. Existing User Flow
1. User logs in → Balance: shows user's balance
2. User connects wallet → Wallet verified or warning shown
3. User can purchase/spend with proper wallet connection

### 3. Wallet Mismatch Flow
1. User logs in → Balance: shows user's balance
2. User connects wrong wallet → Warning displayed
3. System recommends connecting correct wallet
4. User can still see balance but gets security warnings

This architecture provides a clear separation between user ownership and wallet interfaces while maintaining security and providing a smooth user experience.
