-- =====================================================
-- DASWOS WALLET DATABASE SCHEMA (UPDATED)
-- Separate database for wallet functionality
-- Used by both daswos-18 and current-brobot-1
-- UPDATED: Wallets no longer store balances - balances are in user accounts
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- WALLETS TABLE (UPDATED - NO BALANCE STORAGE)
-- =====================================================
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id TEXT NOT NULL UNIQUE, -- User-chosen wallet ID
    password_hash TEXT NOT NULL, -- Scrypt hashed password
    -- REMOVED: balance field - balances are now stored in user accounts in main databases
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Security fields
    failed_login_attempts INTEGER DEFAULT 0 NOT NULL,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    creation_ip INET,
    last_login_ip INET,
    
    -- Constraints
    CONSTRAINT wallet_id_length CHECK (char_length(wallet_id) >= 3 AND char_length(wallet_id) <= 50)
    -- REMOVED: balance_non_negative constraint since balance is no longer stored here
);

-- =====================================================
-- WALLET SESSIONS TABLE (UNCHANGED)
-- =====================================================
CREATE TABLE wallet_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL UNIQUE,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- =====================================================
-- TRANSACTIONS TABLE (UPDATED - NO BALANCE TRACKING)
-- =====================================================
-- NOTE: This table is now primarily for audit/logging purposes
-- Actual balance changes happen in the main database user accounts
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL, -- 'interface_access', 'balance_check', 'purchase_attempt', 'spend_attempt'
    amount DECIMAL(15, 2), -- Amount involved (for reference only)
    -- REMOVED: balance_before and balance_after fields since wallets don't store balances
    description TEXT,
    reference_id TEXT, -- Reference to external transaction (purchase ID, etc.)
    reference_type TEXT, -- 'purchase', 'spend', 'balance_check', 'user_login'
    
    -- Connection to main database
    database_name TEXT, -- Which main database this transaction relates to
    user_id INTEGER, -- User ID in the main database
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    ip_address INET,
    user_agent TEXT,
    
    -- Status
    status TEXT DEFAULT 'completed' NOT NULL, -- 'pending', 'completed', 'failed', 'cancelled'
    
    -- Constraints
    CONSTRAINT valid_transaction_type CHECK (transaction_type IN ('interface_access', 'balance_check', 'purchase_attempt', 'spend_attempt', 'user_connection')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    CONSTRAINT valid_database_name CHECK (database_name IN ('daswos-18', 'current-brobot-1') OR database_name IS NULL)
);

-- =====================================================
-- WALLET CONNECTIONS TABLE (ENHANCED)
-- Link wallets to user accounts in main databases
-- =====================================================
CREATE TABLE wallet_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES wallets(id) ON DELETE CASCADE,
    
    -- Connection to main database
    database_name TEXT NOT NULL, -- 'daswos-18', 'current-brobot-1'
    user_id INTEGER NOT NULL, -- User ID in the main database (now required)
    username TEXT, -- Username in the main database (for reference)
    
    -- Connection metadata
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_primary BOOLEAN DEFAULT false NOT NULL, -- Is this the primary wallet for this user?
    is_active BOOLEAN DEFAULT true NOT NULL, -- Is this connection currently active?
    
    -- Constraints
    CONSTRAINT valid_database_name CHECK (database_name IN ('daswos-18', 'current-brobot-1')),
    UNIQUE(database_name, user_id, wallet_id) -- Prevent duplicate connections
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Wallets indexes
CREATE INDEX idx_wallets_wallet_id ON wallets(wallet_id);
CREATE INDEX idx_wallets_created_at ON wallets(created_at);
CREATE INDEX idx_wallets_is_active ON wallets(is_active);

-- Sessions indexes
CREATE INDEX idx_wallet_sessions_wallet_id ON wallet_sessions(wallet_id);
CREATE INDEX idx_wallet_sessions_session_token ON wallet_sessions(session_token);
CREATE INDEX idx_wallet_sessions_expires_at ON wallet_sessions(expires_at);
CREATE INDEX idx_wallet_sessions_is_active ON wallet_sessions(is_active);

-- Transactions indexes
CREATE INDEX idx_transactions_wallet_id ON transactions(wallet_id);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_transaction_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_reference_id ON transactions(reference_id);
CREATE INDEX idx_transactions_database_user ON transactions(database_name, user_id);

-- Connections indexes
CREATE INDEX idx_wallet_connections_wallet_id ON wallet_connections(wallet_id);
CREATE INDEX idx_wallet_connections_database_user ON wallet_connections(database_name, user_id);
CREATE INDEX idx_wallet_connections_is_primary ON wallet_connections(is_primary);
CREATE INDEX idx_wallet_connections_is_active ON wallet_connections(is_active);

-- =====================================================
-- UPDATED TRIGGERS AND FUNCTIONS
-- =====================================================

-- Function to update wallet last_accessed timestamp
CREATE OR REPLACE FUNCTION update_wallet_last_accessed()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE wallets 
    SET last_accessed = NOW(), updated_at = NOW()
    WHERE id = NEW.wallet_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for wallet access tracking
CREATE TRIGGER update_wallet_access_trigger
    AFTER INSERT ON wallet_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_last_accessed();

-- Function to update connection last_used timestamp
CREATE OR REPLACE FUNCTION update_connection_last_used()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE wallet_connections 
    SET last_used = NOW()
    WHERE wallet_id = NEW.wallet_id 
    AND database_name = NEW.database_name 
    AND user_id = NEW.user_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for connection usage tracking
CREATE TRIGGER update_connection_usage_trigger
    AFTER INSERT ON transactions
    FOR EACH ROW
    WHEN (NEW.database_name IS NOT NULL AND NEW.user_id IS NOT NULL)
    EXECUTE FUNCTION update_connection_last_used();

-- =====================================================
-- SAMPLE DATA FOR TESTING (UPDATED)
-- =====================================================

-- Demo wallet (password: demo123) - NO BALANCE STORED
-- Scrypt hash for 'demo123' with salt
INSERT INTO wallets (wallet_id, password_hash) VALUES 
('demo-wallet', 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890.1234567890abcdef1234567890abcdef12345678');

-- Sample transaction for demo wallet (interface access only)
INSERT INTO transactions (wallet_id, transaction_type, description, reference_type)
SELECT 
    w.id,
    'interface_access',
    'Demo wallet interface access',
    'user_connection'
FROM wallets w WHERE w.wallet_id = 'demo-wallet';

-- =====================================================
-- UPDATED VIEWS FOR EASY QUERYING
-- =====================================================

-- Wallet summary view (updated - no balance)
CREATE VIEW wallet_summary AS
SELECT 
    w.id,
    w.wallet_id,
    w.created_at,
    w.last_accessed,
    w.is_active,
    COUNT(t.id) as transaction_count,
    MAX(t.created_at) as last_transaction_date,
    COUNT(wc.id) as connection_count,
    MAX(wc.last_used) as last_connection_used
FROM wallets w
LEFT JOIN transactions t ON w.id = t.wallet_id
LEFT JOIN wallet_connections wc ON w.id = wc.wallet_id AND wc.is_active = true
WHERE w.is_active = true
GROUP BY w.id, w.wallet_id, w.created_at, w.last_accessed, w.is_active;

-- Recent transactions view (updated)
CREATE VIEW recent_transactions AS
SELECT 
    t.id,
    w.wallet_id,
    t.transaction_type,
    t.amount,
    t.description,
    t.database_name,
    t.user_id,
    t.created_at
FROM transactions t
JOIN wallets w ON t.wallet_id = w.id
WHERE t.status = 'completed'
ORDER BY t.created_at DESC;

-- Active connections view
CREATE VIEW active_connections AS
SELECT 
    wc.id,
    w.wallet_id,
    wc.database_name,
    wc.user_id,
    wc.username,
    wc.is_primary,
    wc.connected_at,
    wc.last_used
FROM wallet_connections wc
JOIN wallets w ON wc.wallet_id = w.id
WHERE wc.is_active = true AND w.is_active = true
ORDER BY wc.last_used DESC;

-- =====================================================
-- COMMENTS (UPDATED)
-- =====================================================

COMMENT ON TABLE wallets IS 'Main wallets table storing wallet credentials (NO BALANCE - balances stored in main databases)';
COMMENT ON TABLE wallet_sessions IS 'Active wallet sessions for authentication';
COMMENT ON TABLE transactions IS 'Wallet interface access and transaction attempts (audit log only)';
COMMENT ON TABLE wallet_connections IS 'Links wallets to user accounts in main databases';

COMMENT ON COLUMN wallets.wallet_id IS 'User-chosen unique wallet identifier';
COMMENT ON COLUMN wallets.password_hash IS 'Scrypt hashed password for wallet access';
COMMENT ON COLUMN transactions.reference_id IS 'External reference (purchase ID, etc.)';
COMMENT ON COLUMN wallet_connections.database_name IS 'Which main database this connection is for';
COMMENT ON COLUMN wallet_connections.user_id IS 'User ID in the main database (required for balance access)';

-- =====================================================
-- MIGRATION NOTES
-- =====================================================
-- 1. Remove balance column from existing wallets table
-- 2. Update transactions table to remove balance tracking
-- 3. Enhance wallet_connections table with user_id requirement
-- 4. Update all application code to get balances from main database
-- 5. Wallets now serve as authentication/interface only
-- =====================================================
