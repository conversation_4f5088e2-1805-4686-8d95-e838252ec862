import express from 'express';
import { createClient } from '@supabase/supabase-js';
import { TransactionLogger } from '../transaction-logger';

const router = express.Router();

// Wallet database configuration
const WALLET_DATABASE_CONFIG = {
  SUPABASE_URL: 'https://mjyaqqsxhkqyzqufpxzl.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qeWFxcXN4aGtxeXpxdWZweHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMzU4MiwiZXhwIjoyMDY0MTk5NTgyfQ.1LBqR-7c8UdBa8koW69nQrYSr_Lm1aErRUgoRteFFVs'
};

// Initialize Supabase client for wallet database
const walletSupabase = createClient(
  WALLET_DATABASE_CONFIG.SUPABASE_URL,
  WALLET_DATABASE_CONFIG.SUPABASE_KEY
);

/**
 * POST /api/wallet/create-connection
 * Create a connection record between a user and a wallet
 */
router.post('/create-connection', async (req, res) => {
  try {
    const {
      wallet_id,
      wallet_identifier,
      database_name,
      user_id,
      username,
      is_primary = false
    } = req.body;

    // Validate required fields
    if (!wallet_id || !database_name || !user_id || !username) {
      return res.status(400).json({
        error: 'Missing required fields: wallet_id, database_name, user_id, username'
      });
    }

    // Validate database name
    if (!['daswos-18', 'current-brobot-1'].includes(database_name)) {
      return res.status(400).json({
        error: 'Invalid database_name. Must be "daswos-18" or "current-brobot-1"'
      });
    }

    console.log('Creating wallet connection:', {
      wallet_id,
      wallet_identifier,
      database_name,
      user_id,
      username,
      is_primary
    });

    // Check if connection already exists
    const { data: existingConnection, error: checkError } = await walletSupabase
      .from('wallet_connections')
      .select('*')
      .eq('wallet_id', wallet_id)
      .eq('database_name', database_name)
      .eq('user_id', user_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.error('Error checking existing connection:', checkError);
      return res.status(500).json({
        error: 'Failed to check existing connection',
        details: checkError.message
      });
    }

    if (existingConnection) {
      // Connection already exists, update last_used timestamp
      const { data: updatedConnection, error: updateError } = await walletSupabase
        .from('wallet_connections')
        .update({
          last_used: new Date().toISOString(),
          is_primary: is_primary
        })
        .eq('id', existingConnection.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating existing connection:', updateError);
        return res.status(500).json({
          error: 'Failed to update existing connection',
          details: updateError.message
        });
      }

      return res.json({
        success: true,
        connection: updatedConnection,
        message: 'Existing connection updated'
      });
    }

    // Create new connection
    const { data: newConnection, error: insertError } = await walletSupabase
      .from('wallet_connections')
      .insert({
        wallet_id,
        database_name,
        user_id,
        username,
        is_primary,
        connected_at: new Date().toISOString(),
        last_used: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating wallet connection:', insertError);
      return res.status(500).json({
        error: 'Failed to create wallet connection',
        details: insertError.message
      });
    }

    console.log('Wallet connection created successfully:', newConnection);

    res.json({
      success: true,
      connection: newConnection,
      message: 'Wallet connection created successfully'
    });

  } catch (error) {
    console.error('Wallet connection creation error:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * GET /api/wallet/connections/:user_id
 * Get all wallet connections for a user
 */
router.get('/connections/:user_id', async (req, res) => {
  try {
    const { user_id } = req.params;
    const { database_name = 'daswos-18' } = req.query;

    const { data: connections, error } = await walletSupabase
      .from('wallet_connections')
      .select(`
        *,
        wallets:wallet_id (
          wallet_id,
          balance,
          created_at,
          is_active
        )
      `)
      .eq('user_id', user_id)
      .eq('database_name', database_name)
      .order('connected_at', { ascending: false });

    if (error) {
      console.error('Error fetching wallet connections:', error);
      return res.status(500).json({
        error: 'Failed to fetch wallet connections',
        details: error.message
      });
    }

    res.json({
      success: true,
      connections
    });

  } catch (error) {
    console.error('Error fetching wallet connections:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * DELETE /api/wallet/disconnect/:connection_id
 * Remove a wallet connection
 */
router.delete('/disconnect/:connection_id', async (req, res) => {
  try {
    const { connection_id } = req.params;

    const { error } = await walletSupabase
      .from('wallet_connections')
      .delete()
      .eq('id', connection_id);

    if (error) {
      console.error('Error disconnecting wallet:', error);
      return res.status(500).json({
        error: 'Failed to disconnect wallet',
        details: error.message
      });
    }

    res.json({
      success: true,
      message: 'Wallet disconnected successfully'
    });

  } catch (error) {
    console.error('Error disconnecting wallet:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * POST /api/wallet/add-funds
 * Record a fund addition transaction with user tracking
 */
router.post('/add-funds', async (req, res) => {
  try {
    const {
      wallet_id,
      amount,
      user_id,
      username,
      database_name = 'daswos-18',
      description = 'Funds added via DasWos Coins purchase'
    } = req.body;

    // Validate required fields
    if (!wallet_id || !amount || !user_id || !username) {
      return res.status(400).json({
        error: 'Missing required fields: wallet_id, amount, user_id, username'
      });
    }

    // Get current wallet balance
    const { data: wallet, error: walletError } = await walletSupabase
      .from('wallets')
      .select('balance')
      .eq('id', wallet_id)
      .single();

    if (walletError) {
      console.error('Error fetching wallet:', walletError);
      return res.status(500).json({
        error: 'Failed to fetch wallet information',
        details: walletError.message
      });
    }

    const balanceBefore = parseFloat(wallet.balance);
    const balanceAfter = balanceBefore + parseFloat(amount);

    // Create transaction record
    const { data: transaction, error: transactionError } = await walletSupabase
      .from('transactions')
      .insert({
        wallet_id,
        transaction_type: 'add_funds',
        amount: parseFloat(amount),
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        description,
        reference_type: 'daswos_coins_purchase',
        reference_id: `user_${user_id}_${Date.now()}`,
        status: 'completed'
      })
      .select()
      .single();

    if (transactionError) {
      console.error('Error creating transaction:', transactionError);
      return res.status(500).json({
        error: 'Failed to record transaction',
        details: transactionError.message
      });
    }

    // Update wallet balance
    const { error: updateError } = await walletSupabase
      .from('wallets')
      .update({
        balance: balanceAfter,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet_id);

    if (updateError) {
      console.error('Error updating wallet balance:', updateError);
      return res.status(500).json({
        error: 'Failed to update wallet balance',
        details: updateError.message
      });
    }

    // Update connection last_used timestamp
    await walletSupabase
      .from('wallet_connections')
      .update({ last_used: new Date().toISOString() })
      .eq('wallet_id', wallet_id)
      .eq('user_id', user_id)
      .eq('database_name', database_name);

    console.log(`Funds added: ${amount} DasWos Coins to wallet ${wallet_id} by user ${username} (${user_id})`);

    res.json({
      success: true,
      transaction,
      new_balance: balanceAfter,
      message: `Successfully added ${amount} DasWos Coins to wallet`
    });

  } catch (error) {
    console.error('Error adding funds:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * GET /api/wallet/fund-history/:wallet_id
 * Get fund addition history for a wallet with user information
 */
router.get('/fund-history/:wallet_id', async (req, res) => {
  try {
    const { wallet_id } = req.params;

    // Get transactions with connection information
    const { data: transactions, error } = await walletSupabase
      .from('transactions')
      .select(`
        *,
        wallet_connections!inner (
          user_id,
          username,
          database_name
        )
      `)
      .eq('wallet_id', wallet_id)
      .eq('transaction_type', 'add_funds')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching fund history:', error);
      return res.status(500).json({
        error: 'Failed to fetch fund history',
        details: error.message
      });
    }

    res.json({
      success: true,
      transactions
    });

  } catch (error) {
    console.error('Error fetching fund history:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
});

/**
 * POST /api/wallet/login
 * Login to existing wallet
 */
router.post('/login', async (req, res) => {
  try {
    const { walletId, password, userId, username } = req.body;

    console.log(`🔐 Wallet login attempt: ${walletId} for user ${username} (ID: ${userId})`);

    // Validate inputs
    if (!walletId || !password) {
      return res.status(400).json({ error: 'Wallet ID and password are required' });
    }

    // Hash password using same method as create
    const crypto = await import('crypto');
    const passwordHash = crypto.createHash('sha256').update(password + 'wallet_salt').digest('hex');

    // Check if wallet exists and verify password
    const { data: wallet, error: walletError } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', walletId)
      .eq('password_hash', passwordHash)
      .eq('is_active', true)
      .single();

    if (walletError || !wallet) {
      console.log(`❌ Wallet login failed: ${walletId} - Invalid credentials`);
      return res.status(401).json({ error: 'Invalid wallet ID or password' });
    }

    console.log(`✅ Wallet login successful: ${walletId} for user ${username}`);

    // Log wallet connection
    if (userId && username) {
      TransactionLogger.walletConnect(parseInt(userId), username, walletId);
    }

    // Update last accessed time
    await walletSupabase
      .from('wallets')
      .update({ last_accessed: new Date().toISOString() })
      .eq('wallet_id', walletId);

    // Create wallet session
    const sessionToken = 'wallet_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    const { data: sessionData, error: sessionError } = await walletSupabase
      .from('wallet_sessions')
      .insert({
        wallet_id: walletId,
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Failed to create wallet session:', sessionError);
      // Don't fail the request - wallet login was successful
    }

    // Create wallet connection record if user info provided
    if (userId && username) {
      const { data: existingConnection } = await walletSupabase
        .from('wallet_connections')
        .select('*')
        .eq('wallet_id', walletId)
        .eq('user_id', userId)
        .eq('database_name', 'daswos-18')
        .single();

      if (!existingConnection) {
        await walletSupabase
          .from('wallet_connections')
          .insert({
            wallet_id: walletId,
            user_id: userId,
            username: username,
            database_name: 'daswos-18',
            connected_at: new Date().toISOString(),
            is_active: true,
            is_primary: true
          });
      } else {
        // Update last used time
        await walletSupabase
          .from('wallet_connections')
          .update({ last_used: new Date().toISOString() })
          .eq('id', existingConnection.id);
      }
    }

    res.json({
      success: true,
      wallet: {
        id: wallet.id,
        wallet_id: wallet.wallet_id,
        created_at: wallet.created_at,
        last_accessed: wallet.last_accessed
      },
      session: {
        token: sessionToken,
        expires_at: expiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Error logging into wallet:', error);
    res.status(500).json({ error: 'Failed to login to wallet' });
  }
});

/**
 * POST /api/wallet/create
 * Create/Activate wallet with password
 */
router.post('/create', async (req, res) => {
  try {
    const { walletId, password } = req.body;

    // For now, we'll extract user info from headers or session
    // In a real implementation, this would use proper authentication middleware
    const userId = req.body.userId || req.headers['x-user-id'];
    const username = req.body.username || req.headers['x-username'];

    // Validate inputs
    if (!walletId || !password) {
      return res.status(400).json({ error: 'Wallet ID and password are required' });
    }

    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters' });
    }

    // Hash password using simple method (in production, use proper scrypt)
    const crypto = await import('crypto');
    const passwordHash = crypto.createHash('sha256').update(password + 'wallet_salt').digest('hex');

    // Check if wallet exists and update password, or create new wallet
    const { data: existingWallet, error: checkError } = await walletSupabase
      .from('wallets')
      .select('*')
      .eq('wallet_id', walletId)
      .single();

    let walletData;
    if (existingWallet) {
      // Check if this wallet has no password yet (password_hash is null or undefined)
      const needsPassword = !existingWallet.password_hash;

      // Update existing wallet with password
      const { data: updatedWallet, error: updateError } = await walletSupabase
        .from('wallets')
        .update({
          password_hash: passwordHash,
          is_active: true, // Ensure wallet is active
          last_accessed: new Date().toISOString()
        })
        .eq('wallet_id', walletId)
        .select()
        .single();

      if (updateError) {
        console.error('Failed to update wallet password:', updateError);
        return res.status(500).json({ error: 'Failed to activate wallet' });
      }

      walletData = updatedWallet;
      console.log(`✅ Wallet ${needsPassword ? 'password set for first time' : 'password updated'} for wallet: ${walletId}`);
    } else {
      // Create new wallet with password
      const { data: newWallet, error: createError } = await walletSupabase
        .from('wallets')
        .insert({
          wallet_id: walletId,
          password_hash: passwordHash,
          is_active: true,
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('Failed to create wallet:', createError);
        return res.status(500).json({ error: 'Failed to create wallet' });
      }

      walletData = newWallet;
      console.log(`✅ New wallet created: ${walletId}`);
    }

    // Create wallet session
    const sessionToken = 'wallet_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    const { data: sessionData, error: sessionError } = await walletSupabase
      .from('wallet_sessions')
      .insert({
        wallet_id: walletId,
        session_token: sessionToken,
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Failed to create wallet session:', sessionError);
      // Don't fail the request - wallet was created successfully
    }

    // Create wallet connection if it doesn't exist and we have user info
    if (userId && username) {
      const { data: existingConnection } = await walletSupabase
        .from('wallet_connections')
        .select('*')
        .eq('wallet_id', walletId)
        .eq('user_id', userId)
        .eq('database_name', 'daswos-18')
        .single();

      if (!existingConnection) {
        await walletSupabase
          .from('wallet_connections')
          .insert({
            wallet_id: walletId,
            user_id: userId,
            username: username,
            database_name: 'daswos-18',
            connected_at: new Date().toISOString(),
            is_active: true,
            is_primary: true
          });
      }
    }

    res.json({
      success: true,
      wallet: {
        id: walletData.id,
        wallet_id: walletData.wallet_id,
        created_at: walletData.created_at,
        last_accessed: walletData.last_accessed
      },
      session: {
        token: sessionToken,
        expires_at: expiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Error creating/activating wallet:', error);
    res.status(500).json({ error: 'Failed to create wallet' });
  }
});

export default router;
